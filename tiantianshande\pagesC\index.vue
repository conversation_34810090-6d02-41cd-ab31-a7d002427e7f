<template>
  <view class="container">
    <!-- 主页面容器 -->
    <view class="page">
      <!-- 头部区域 -->
      <HeaderComponent />
      
      <!-- 中间内容区域 -->
      <view class="main-content">
        <!-- 聊天对话区域 -->
        <ChatComponent
          :messages="messages"
          @add-message="handleAddMessage"
          @question-click="handleQuestionClick"
          @form-generating="handleFormGenerating"
          @form-complete="handleFormComplete"
        />
      </view>
      
      <!-- 底部输入框区域 -->
      <InputComponent 
        @send-message="handleSendMessage"
        @quick-action="handleQuickAction"
      />
    </view>
    
    <!-- 加载组件 -->
    <LoadingComponent :show="loading" :text="loadingText" />
  </view>
</template>

<script>
// 引入组件
import HeaderComponent from './components/HeaderComponent.vue'
import ChatComponent from './components/ChatComponent.vue'
import InputComponent from './components/InputComponent.vue'
import LoadingComponent from './components/LoadingComponent.vue'

var app = getApp();

export default {
  name: 'HealthAssistant',
  components: {
    HeaderComponent,
    ChatComponent,
    InputComponent,
    LoadingComponent
  },
  data() {
    return {
      // 基础数据
      loading: false,
      loadingText: '加载中...',
      pre_url: app.globalData.pre_url,
      
      // 聊天相关数据
      messages: [
        {
          id: 1,
          type: 'user',
          messageType: 'text',
          content: '为我生成个性化的训练计划',
          timestamp: Date.now()
        },
        {
          id: 2,
          type: 'bot',
          messageType: 'text',
          content: '你好，我是你的运动健康小助手。不知道如何训练，来问一问，帮你制定专属训练计划，伴你科学运动，避免运动误区',
          timestamp: Date.now()
        },
        {
          id: 3,
          type: 'bot',
          messageType: 'intro',
          timestamp: Date.now()
        }
      ],
      
      // 表单相关数据
      formData: {},
      
      // 状态管理
      isProcessing: false,
      currentStep: 1,
      totalSteps: 4
    }
  },
  
  onLoad(options) {
    console.log('2025-01-26 10:00:00,001-INFO-[index][onLoad_001] 页面加载开始', options);
    this.initPage();
  },
  
  onShow() {
    console.log('2025-01-26 10:00:00,002-INFO-[index][onShow_001] 页面显示');
  },
  
  methods: {
    // 动态颜色函数
    t(colorKey) {
      const colorMap = {
        'color1': '#5F58FF',
        'color1rgb': '95, 88, 255'
      };
      return colorMap[colorKey] || colorKey;
    },

    // 初始化页面
    initPage() {
      console.log('2025-01-26 10:00:00,003-INFO-[index][initPage_001] 初始化页面数据');
      // 这里可以添加初始化逻辑，比如获取用户信息、配置数据等
    },
    
    // 处理添加消息
    handleAddMessage(message) {
      console.log('2025-01-26 10:00:00,004-INFO-[index][handleAddMessage_001] 添加消息:', message);
      this.messages.push({
        id: Date.now(),
        messageType: 'text', // 默认为文本消息
        ...message,
        timestamp: Date.now()
      });

      // 确保滚动到最新消息
      this.$nextTick(() => {
        setTimeout(() => {
          // 触发页面滚动到底部
          uni.pageScrollTo({
            scrollTop: 99999,
            duration: 300
          });
        }, 100);
      });
    },
    
    // 处理问题点击
    handleQuestionClick(question) {
      console.log('2025-01-26 10:00:00,005-INFO-[index][handleQuestionClick_001] 问题点击:', question);
      
      // 添加用户问题到聊天区域
      this.handleAddMessage({
        type: 'user',
        content: question
      });
      
      // 模拟AI回复
      this.simulateAIResponse(question);
    },
    
    // 处理发送消息
    handleSendMessage(message) {
      console.log('2025-01-26 10:00:00,006-INFO-[index][handleSendMessage_001] 发送消息:', message);
      
      if (this.isProcessing) return;
      
      // 添加用户消息
      this.handleAddMessage({
        type: 'user',
        content: message
      });
      
      // 模拟AI回复
      this.simulateAIResponse(message);
    },
    
    // 处理快捷操作
    handleQuickAction(action) {
      console.log('2025-01-26 10:00:00,007-INFO-[index][handleQuickAction_001] 快捷操作:', action);
      console.log('2025-01-26 10:00:00,008-INFO-[index][handleQuickAction_002] 操作类型:', action?.type);

      if (!action || !action.type) {
        console.error('2025-01-26 10:00:00,009-ERROR-[index][handleQuickAction_003] 无效的操作对象:', action);
        return;
      }

      switch(action.type) {
        case 'exercise':
          console.log('2025-01-26 10:00:00,010-INFO-[index][handleQuickAction_004] 执行运动计划');
          this.showExercisePlan();
          break;
        case 'assessment':
          console.log('2025-01-26 10:00:00,011-INFO-[index][handleQuickAction_005] 执行肩颈评估');
          this.startAssessment();
          break;
        case 'nutrition':
          console.log('2025-01-26 10:00:00,012-INFO-[index][handleQuickAction_006] 执行营养建议');
          this.showNutritionAdvice();
          break;
        default:
          console.log('2025-01-26 10:00:00,013-INFO-[index][handleQuickAction_007] 默认处理:', action.text);
          this.handleSendMessage(action.text || '功能开发中');
      }
    },
    
    // 处理表单生成中
    handleFormGenerating() {
      console.log('2025-01-26 10:00:00,008-INFO-[index][handleFormGenerating_001] 表单生成中');

      // 显示生成中消息
      this.handleAddMessage({
        type: 'bot',
        content: '正在根据您的信息制定个性化训练方案，请稍候...'
      });
    },

    // 处理表单完成
    handleFormComplete(formData) {
      console.log('2025-01-26 10:00:00,009-INFO-[index][handleFormComplete_001] 表单完成:', formData);
      this.formData = formData;

      // 显示AI思考状态
      this.handleAddMessage({
        type: 'bot',
        content: '正在生成您的个性化运动计划...',
        isTyping: true
      });

      // 模拟AI思考时间
      setTimeout(() => {
        // 移除思考消息
        this.messages = this.messages.filter(msg => !msg.isTyping);

        // 逐条显示运动计划
        if (formData.plan && formData.plan.length > 0) {
          formData.plan.forEach((planItem, index) => {
            setTimeout(() => {
              this.handleAddMessage({
                type: 'bot',
                content: planItem
              });
            }, index * 800);
          });
        }
      }, 1500);
    },
    
    // 模拟AI回复
    simulateAIResponse(userMessage) {
      this.isProcessing = true;
      
      // 显示正在思考
      this.handleAddMessage({
        type: 'bot',
        content: '正在思考...',
        isTyping: true
      });
      
      // 模拟延迟回复
      setTimeout(() => {
        // 移除思考消息
        this.messages = this.messages.filter(msg => !msg.isTyping);
        
        // 添加AI回复
        const response = this.generateAIResponse(userMessage);
        this.handleAddMessage({
          type: 'bot',
          content: response
        });
        
        this.isProcessing = false;
      }, 1500);
    },
    
    // 生成AI回复
    generateAIResponse(userMessage) {
      const responses = {
        '减肥的误区': '减肥常见误区包括：1.节食减肥容易反弹 2.只做有氧运动效果有限 3.减肥药物存在健康风险 4.过度运动可能适得其反',
        '运动计划': '制定科学运动计划需要考虑：1.个人体质和健康状况 2.从低强度开始循序渐进 3.结合有氧和力量训练 4.充分热身和恢复',
        '恢复': '运动后正确恢复方法：1.充分拉伸放松肌肉 2.补充足够水分 3.保证充足睡眠 4.适当蛋白质摄入'
      };
      
      // 根据关键词匹配回复
      for (let key in responses) {
        if (userMessage.includes(key)) {
          return responses[key];
        }
      }
      
      return '这是一个很好的问题，我来为您详细解答。作为您的运动健康小助手，我会根据您的具体情况提供专业建议。';
    },
    
    // 显示运动计划
    showExercisePlan() {
      this.handleAddMessage({
        type: 'user',
        content: '运动计划'
      });

      this.handleAddMessage({
        type: 'bot',
        content: '我来为您制定个性化运动计划，请先完成下面的问卷调查。'
      });

      // 添加新的表单消息到聊天流
      setTimeout(() => {
        this.handleAddMessage({
          type: 'bot',
          messageType: 'form',
          timestamp: Date.now()
        });
      }, 1000);
    },
    
    // 开始评估
    startAssessment() {
      this.handleAddMessage({
        type: 'user',
        content: '肩颈评估'
      });
      
      this.handleAddMessage({
        type: 'bot',
        content: '我将为您进行肩颈健康评估，请描述您是否有肩颈不适的症状。'
      });
    },
    
    // 显示营养建议
    showNutritionAdvice() {
      this.handleAddMessage({
        type: 'user',
        content: '营养建议'
      });
      
      this.handleAddMessage({
        type: 'bot',
        content: '我来为您提供科学的饮食指导，请告诉我您的饮食习惯和健康目标。'
      });
    }
  }
}
</script>

<style>
/* 基础样式 */
page {
  background: linear-gradient(180deg, rgba(237, 234, 255, 1) 0%, rgba(239, 236, 255, 1) 100%);
  width: 100%;
  height: 100%;
}

.container {
  width: 100%;
  height: 100%;
}

/* 页面主容器 */
.page {
  background: linear-gradient(180deg, rgba(237, 234, 255, 1) 0%, rgba(239, 236, 255, 1) 100%);
  position: relative;
  width: 100%;
  min-height: 100vh;
  padding: 20rpx 20rpx 0;
  box-sizing: border-box;
}

/* 中间内容区域 */
.main-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 200rpx);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .page {
    padding: 20rpx 16rpx 0;
  }
}
</style>
