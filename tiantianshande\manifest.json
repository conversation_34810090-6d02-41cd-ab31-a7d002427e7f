{"name": "赋康源", "appid": "__UNI__8BC40A6", "description": "", "versionName": "1.10", "versionCode": 280, "transformPx": false, "app-plus": {"compatible": {"ignoreVersion": true}, "usingComponents": true, "nvueCompiler": "uni-app", "compilerVersion": 3, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {"Share": {}, "VideoPlayer": {}, "Payment": {}, "Camera": {}, "OAuth": {}}, "distribute": {"android": {"permissionPhoneState": {"request": "none", "prompt": "需要获取设备信息,统计app使用率"}, "permissionExternalStorage": {"request": "none", "prompt": "应用需要保存运行状态，用户信息等数据到手机。"}, "permissions": ["<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.INTERNET\"/>", "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"], "abiFilters": ["armeabi-v7a", "arm64-v8a"]}, "ios": {"capabilities": {"entitlements": {"com.apple.developer.associated-domains": ["applinks:v2ulink.diandashop.com", "applinks:static-7c82a81f-93f5-4c76-aa1d-9f56479b257e.bspapp.com", "applinks:ulink.diandashop.com"]}}, "idfa": false, "privacyDescription": {"NSPhotoLibraryUsageDescription": "从相册中选择图片作为用户头像及商品评价等", "NSLocationWhenInUseUsageDescription": "用于展示店铺距离", "NSCameraUsageDescription": "拍摄图片作为用户头像及商品评价等", "NSPhotoLibraryAddUsageDescription": "保存商品海报等图片到手机", "NSMicrophoneUsageDescription": "录音上传到用户点评", "NSLocationAlwaysUsageDescription": "实时获取位置进行定位", "NSLocationAlwaysAndWhenInUseUsageDescription": "获取当前位置用于展示距离"}, "dSYMs": false}, "sdkConfigs": {"ad": {}, "oauth": {"weixin": {"appid": "wxd441dec22ba4fdff", "appsecret": "", "UniversalLinks": "https://ulink.diandashop.com/uni-universallinks/__UNI__9080613"}}, "share": {"weixin": {"appid": "wxd441dec22ba4fdff", "UniversalLinks": "https://ulink.diandashop.com/uni-universallinks/__UNI__9080613"}}, "payment": {"weixin": {"__platform__": ["ios", "android"], "appid": "wxd441dec22ba4fdff", "UniversalLinks": "https://ulink.diandashop.com/uni-universallinks/__UNI__9080613"}, "alipay": {"__platform__": ["ios", "android"]}}, "maps": {"amap": {"name": "", "appkey_ios": "61e75cacb365d682d08f36c2862c5a50", "appkey_android": "78a3f4dde65c22f0192398c7f8b09483"}}}, "icons": {"android": {"hdpi": "unpackage/res/icons/72x72.png", "xhdpi": "unpackage/res/icons/96x96.png", "xxhdpi": "unpackage/res/icons/144x144.png", "xxxhdpi": "unpackage/res/icons/192x192.png"}, "ios": {"appstore": "unpackage/res/icons/1024x1024.png", "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "notific__UNI__9080613ation": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png", "proapp@2x": "unpackage/res/icons/167x167.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "notification": "unpackage/res/icons/20x20.png"}, "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png"}}}}}, "quickapp": {}, "mp-weixin": {"appid": "wx17b0fc76d4ba099d", "setting": {"urlCheck": false}, "usingComponents": true, "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序位置接口的效果展示"}}, "plugins": {}, "requiredPrivateInfos": ["getLocation", "<PERSON><PERSON><PERSON><PERSON>", "chooseLocation"], "optimization": {"subPackages": true}, "requiredBackgroundModes": ["audio", "backgroundFetch"], "lazyCodeLoading": "requiredComponents"}, "mp-alipay": {"usingComponents": true, "appid": "2021002142648644"}, "mp-baidu": {"usingComponents": true, "appid": "23935205"}, "mp-toutiao": {"usingComponents": true, "appid": "ttdf53a9ee406be5c001"}, "h5": {"router": {"base": "./"}, "title": "首页", "sdkConfigs": {"maps": {"amap": {"key": "1e3e8e3ffaeeefd61e11fec5037dc31a", "securityJsCode": "6cbec2c4fd0dda187fae2b8cb74031a3", "serviceHost": ""}}}, "devServer": {"https": false}}, "mp-qq": {"appid": "1111614727", "setting": {"es6": true, "minified": true}}, "_spaceID": "de1419c6-c6c9-4817-a01a-f508977ee3df", "vueVersion": "3"}