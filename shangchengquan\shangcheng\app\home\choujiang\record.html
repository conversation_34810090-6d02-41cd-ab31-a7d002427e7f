<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>抽奖记录</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  {include file="public/css"/}
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-card layui-col-md12">
          <div class="layui-card-header">
						<i class="fa fa-list"></i> 抽奖记录
						{if input('param.isopen')==1}<i class="layui-icon layui-icon-close" style="font-size:18px;font-weight:bold;cursor:pointer" onclick="closeself()"></i>{/if}
					</div>
          <div class="layui-card-body" pad15>
						<div class="layui-col-md3" style="padding-bottom:10px">
							<button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="datadel(0)">删除</button>
							<button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="location.href='{:url('recordexcel')}/hid/{$Request.param.hid}'+urlEncode(datawhere)">导出</button>
						</div>
						<div class="layui-form layui-col-md9" style="text-align:right;padding-bottom:10px">
							<div class="layui-inline layuiadmin-input-useradmin">
								<label class="layui-form-label" style="width:60px">{:t('会员')}ID</label>
								<div class="layui-input-block" style="width:90px;margin-left:90px">
									<input type="text" name="mid" autocomplete="off" class="layui-input" value="{$Request.param.mid}">
								</div>
							</div>
							<div class="layui-inline layuiadmin-input-useradmin">
								<label class="layui-form-label" style="width:90px">兑奖信息</label>
								<div class="layui-input-block" style="width:120px;margin-left:120px">
									<input type="text" name="linkman" autocomplete="off" class="layui-input" value="">
								</div>
							</div>
							<div class="layui-inline layuiadmin-input-useradmin">
								<label class="layui-form-label" style="width:30px">奖品</label>
								<div class="layui-input-block" style="width:120px;margin-left:60px">
									<input type="text" name="jxmc" autocomplete="off" class="layui-input" value="">
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label" style="width:30px;">时间</label>
								<div class="layui-input-block" style="width:180px;margin-left:60px">
									<input type="text" name="ctime" id="ctime" autocomplete="off" class="layui-input">
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label" style="width:30px">状态</label>
								<div class="layui-input-block" style="width:120px;margin-left:60px;text-align:left">
									<select name="status">
										<option value="">全部</option>
										<option value="1">已领取</option>
										<option value="0">未领取</option>
									</select>
								</div>
							</div>
							<div class="layui-inline">
								<button class="layui-btn layuiadmin-btn-replys" lay-submit="" lay-filter="LAY-app-forumreply-search">
									<i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
								</button>
							</div>
						</div>
						<div class="layui-col-md12">
							<table id="tabledata" lay-filter="tabledata"></table>
						</div>
          </div>
        </div>
    </div>
  </div>
	{include file="public/js"/}
	<script>
	layui.laydate.render({ 
		elem: '#ctime',
		trigger: 'click',
		range: '~' //或 range: '~' 来自定义分割字符
	});
  var table = layui.table;
	var form = layui.form;
	var datawhere = {};
  //数据表
  var tableIns = table.render({
    elem: '#tabledata'
    ,url: "{$Request.url}" //数据接口
    ,page: true //开启分页
    ,cols: [[ //表头
			{type:"checkbox"},
      {field: 'hid', title: '活动ID',width:80},
      {field: 'name', title: '活动名称',width:180},
      {field: 'mid', title: '{:t('会员')}ID',width:80},
      {field: 'nickname', title: '{:t('会员')}信息',templet:'<div>{{# if(d.nickname){ }}<img src="{{d.headimg}}" style="width:50px"> {{d.nickname}}{{# } }}</div>'},
      {field: 'jxmc', title: '奖品'},
      {field: 'formdata', title: '兑奖信息'},
      {field: 'express_info', title: '物流信息',width:150,templet:function(d){
				if(d.jx==0) return '<span style="color:#999">-</span>';
				if(d.express_status==0) return '<span style="color:#999">未发货</span>';
				if(d.express_status==1) return '<span style="color:#1890ff">'+d.express_com+'<br>'+d.express_no+'</span>';
				if(d.express_status==2) return '<span style="color:green">已收货</span>';
				return '<span style="color:#999">-</span>';
			}},
      //{field: 'linkman', title: '兑奖信息',templet:function(d){
			//	if(d.linkman){return d.linkman + '('+d.tel+')'}else{ return '';}
			//}},
      {field: 'createtime', title: '时间',sort:true,templet:function(d){ return date('Y-m-d H:i',d.createtime)}},
      {field: 'status', title: '状态',width:80,templet:function(d){
				if(d.jx==0) return '<span style="color:#999">未中奖</span>'
				if(d.status==0) return '<span style="color:red">未领取</span>'
				if(d.status==1) return '<span style="color:green">已领取</span>'
				return '';
			}},
      {field: 'remark', title: '备注',width:120},
      {field: 'op', title: '操作',width:180,templet:function(d){
				var html = '';
				if(d.jx>0 && d.status==0){
					html+='<button class="table-btn" onclick="setst('+d.id+',1)">改为已领取</button>'
				}
				if(d.jx>0 && d.express_status==0){
					html+='<button class="table-btn" onclick="sendExpress('+d.id+')">发货</button>'
				}
				if(d.jx>0 && d.express_status==1){
					html+='<button class="table-btn" onclick="viewExpress('+d.id+')">查物流</button>'
					html+='<button class="table-btn" onclick="sendExpress('+d.id+')">改物流</button>'
				}
				html+='<button class="table-btn" onclick="datadel('+d.id+')">删除</button>'
				return html;
			}}
    ]]
  });
	//排序
	table.on('sort(tabledata)', function(obj){
		datawhere.field = obj.field;
		datawhere.order = obj.type;
		tableIns.reload({
			initSort: obj,
			where: datawhere
		});
	});
	//检索
	layui.form.on('submit(LAY-app-forumreply-search)', function(obj){
		var field = obj.field
		var olddatawhere = datawhere
		datawhere = field
		datawhere.field = olddatawhere.field
		datawhere.order = olddatawhere.order
		tableIns.reload({
			where: datawhere,
			page: {curr: 1}
		});
	})
	//删除
	function datadel(id){
		var ids = [];
		if(id==0){
			var checkStatus = table.checkStatus('tabledata')
			var checkData = checkStatus.data; //得到选中的数据
			if(checkData.length === 0){
				 return layer.msg('请选择数据');
			}
			var ids = [];
			for(var i=0;i<checkData.length;i++){
				ids.push(checkData[i]['id']);
			}
		}else{
			ids.push(id)
		}
		layer.confirm('确定要删除吗?',{icon: 7, title:'操作确认'}, function(index){
			//do something
			layer.close(index);
			var index = layer.load();
			$.post("{:url('recorddel')}",{ids:ids},function(data){
				layer.close(index);
				dialog(data.msg,data.status);
				tableIns.reload()
			})
		});
	}
	//修改状态
	function setst(id,st){
		var ids = [];
		if(id==0){
			var checkStatus = table.checkStatus('tabledata')
			var checkData = checkStatus.data; //得到选中的数据
			if(checkData.length === 0){
				 return layer.msg('请选择数据');
			}
			var ids = [];
			for(var i=0;i<checkData.length;i++){
				ids.push(checkData[i]['id']);
			}
		}else{
			ids.push(id)
		}
		layer.confirm('确定要改为已领奖吗?',{icon: 7, title:'操作确认'}, function(index){
			//do something
			layer.close(index);
			var index = layer.load();
			$.post("{:url('setst')}",{ids:ids,st:st},function(data){
				layer.close(index);
				dialog(data.msg,data.status);
				tableIns.reload()
			})
		});
	}

	//发货
	function sendExpress(recordid){
		var html = '<div style="margin:20px auto;">';
		html+='<div class="layui-form" lay-filter="sendExpressForm">';
		html+='	<div class="layui-form-item" style="margin-top:40px;">';
		html+='		<label class="layui-form-label" style="width:80px">快递公司</label>';
		html+='		<div class="layui-input-inline" style="width:200px">';
		html+='			<select id="express_com" name="express_com" lay-verify="required">';
		html+='				<option value="">请选择快递公司</option>';
		{foreach $express_data as $k=>$v}
		html+='				<option value="{$k}">{$v}</option>';
		{/foreach}
		html+='			</select>';
		html+='		</div>';
		html+='	</div>';
		html+='	<div class="layui-form-item">';
		html+='		<label class="layui-form-label" style="width:80px">快递单号</label>';
		html+='		<div class="layui-input-inline" style="width:200px">';
		html+='			<input type="text" id="express_no" name="express_no" class="layui-input" placeholder="请输入快递单号" lay-verify="required">';
		html+='		</div>';
		html+='	</div>';
		html+='</div>';
		html+='</div>';

		var sendExpressLayer = layer.open({
			type:1,
			title:'奖品发货',
			area:['450px','320px'],
			content:html,
			shadeClose:true,
			btn: ['确定发货', '取消'],
			success: function(layero, index){
				// 渲染layui表单
				form.render('select', 'sendExpressForm');
			},
			yes:function(){
				var express_com = $("#express_com").val();
				var express_no = $("#express_no").val();
				if(!express_com || !express_no){
					layer.msg('请填写完整信息');
					return false;
				}

				var index = layer.load();
				$.post("{:url('sendExpress')}",{recordid:recordid,express_com:express_com,express_no:express_no},function(data){
					layer.close(index);
					layer.close(sendExpressLayer);
					if(data.status==1){
						layer.msg(data.msg, {icon: 1});
						tableIns.reload();
					}else{
						layer.msg(data.msg, {icon: 2});
					}
				})
			}
		});
	}

	//查看物流
	function viewExpress(recordid){
		var index = layer.load();
		$.post("{:url('getExpress')}",{recordid:recordid},function(res){
			layer.close(index);
			if(res.status==0){
				layer.msg(res.msg);
				return;
			}

			var html = '<div style="padding:20px;">';
			html+='<div class="orderinfo">';
			html+='	<div class="item">';
			html+='		<span class="t1">快递公司</span>';
			html+='		<span class="t2">'+res.record.express_com+'</span>';
			html+='	</div>';
			html+='	<div class="item">';
			html+='		<span class="t1">快递单号</span>';
			html+='		<span class="t2">'+res.record.express_no+'</span>';
			html+='	</div>';
			html+='	<div class="item">';
			html+='		<span class="t1">发货时间</span>';
			html+='		<span class="t2">'+date('Y-m-d H:i:s',res.record.send_time)+'</span>';
			html+='	</div>';
			html+='</div>';

			if(res.data && res.data.length > 0){
				html+='<div class="logistics">';
				for(var i=0; i<res.data.length; i++){
					var item = res.data[i];
					var className = i==0 ? 'item on' : 'item';
					html+='	<div class="'+className+'">';
					html+='		<div class="f1"><img src="{$pre_url}/static/img/wuliu.png"></div>';
					html+='		<div class="f2">';
					html+='			<div class="t1">'+item.context+'</div>';
					html+='			<div class="t2">'+item.time+'</div>';
					html+='		</div>';
					html+='	</div>';
				}
				html+='</div>';
			}else{
				html+='<div style="text-align:center;padding:20px;color:#999">暂无物流信息</div>';
			}
			html+='</div>';

			layer.open({type:1,title:'查看物流',content:html,area:['600px','500px'],shadeClose:true,btn: ['修改', '关闭'],
				yes:function(){
					layer.closeAll();
					sendExpress(recordid);
				}
			});
		});
	}
	</script>
	{include file="public/copyright"/}
</body>
</html>