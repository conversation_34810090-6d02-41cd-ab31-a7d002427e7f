<template>
  <view class="chat-input">
    <!-- 快捷按钮区域 -->
    <view class="quick-actions">
      <view class="quick-buttons-container">
        <view
          v-for="(action, index) in quickActions"
          :key="index"
          class="quick-btn"
          @tap="handleQuickAction(action)"
        >
          <image
            v-if="action.icon"
            class="quick-icon"
            :src="pre_url + action.icon"
            mode="aspectFit"
          />
          <text>{{ action.text }}</text>
        </view>
      </view>
    </view>

    <!-- 输入框区域 -->
    <view class="input-container">
      <!-- 默认状态：问答按钮 -->
      <view 
        v-if="!showInput"
        class="qa-btn"
        @tap="showInputBox"
      >
        <view class="qa-icon-wrapper">
          <image 
            class="qa-icon"
            :src="pre_url + '/static/img/chat-icon.png'"
            mode="aspectFit"
          />
        </view>
        <text class="qa-text">点击开始对话</text>
      </view>

      <!-- 输入状态：输入框和发送按钮 -->
      <view 
        v-else
        class="input-wrapper"
      >
        <input 
          class="chat-input-box"
          v-model="inputText"
          placeholder="请输入您的问题..."
          placeholder-style="color: rgba(150, 150, 150, 1)"
          @confirm="handleSend"
          @blur="handleInputBlur"
          :focus="inputFocus"
        />
        <view 
          class="send-btn"
          @tap="handleSend"
        >
          <text>发送</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
var app = getApp();

export default {
  name: 'InputComponent',
  data() {
    return {
      pre_url: app.globalData.pre_url,
      inputText: '',
      showInput: false,
      inputFocus: false,
      
      // 快捷操作配置
      quickActions: [
        {
          id: 1,
          type: 'exercise',
          text: '运动计划',
          icon: '/static/img/exercise-icon.png'
        },
        {
          id: 2,
          type: 'assessment',
          text: '肩颈评估',
          icon: '/static/img/assessment-icon.png'
        },
        {
          id: 3,
          type: 'nutrition',
          text: '营养建议',
          icon: '/static/img/nutrition-icon.png'
        },
        {
          id: 4,
          type: 'sleep',
          text: '睡眠管理',
          icon: '/static/img/sleep-icon.png'
        },
        {
          id: 5,
          type: 'meditation',
          text: '冥想放松',
          icon: '/static/img/meditation-icon.png'
        },
        {
          id: 6,
          type: 'record',
          text: '健康记录',
          icon: '/static/img/record-icon.png'
        }
      ]
    }
  },
  
  methods: {
    // 显示输入框
    showInputBox() {
      console.log('2025-01-26 10:00:00,001-INFO-[InputComponent][showInputBox_001] 显示输入框');
      this.showInput = true;
      this.inputFocus = true;
    },
    
    // 处理输入框失焦
    handleInputBlur() {
      // 延迟隐藏，避免点击发送按钮时输入框消失
      setTimeout(() => {
        if (!this.inputText.trim()) {
          this.showInput = false;
          this.inputFocus = false;
        }
      }, 200);
    },
    
    // 处理发送
    handleSend() {
      const message = this.inputText.trim();
      
      if (!message) {
        uni.showToast({
          title: '请输入内容',
          icon: 'none'
        });
        return;
      }
      
      console.log('2025-01-26 10:00:00,002-INFO-[InputComponent][handleSend_001] 发送消息:', message);
      
      // 向父组件发送消息
      this.$emit('send-message', message);
      
      // 清空输入框
      this.inputText = '';
      this.showInput = false;
      this.inputFocus = false;
    },
    
    // 处理快捷操作
    handleQuickAction(action) {
      console.log('2025-01-26 10:00:00,003-INFO-[InputComponent][handleQuickAction_001] 快捷操作:', action);
      console.log('2025-01-26 10:00:00,004-INFO-[InputComponent][handleQuickAction_002] 操作类型:', action.type);
      console.log('2025-01-26 10:00:00,005-INFO-[InputComponent][handleQuickAction_003] 操作文本:', action.text);

      // 向父组件发送快捷操作事件
      this.$emit('quick-action', action);
    }
  }
}
</script>

<style scoped>
/* 聊天输入框 - 浮动在底部 */
.chat-input {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 1000rpx;
  padding: 20rpx 30rpx 30rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-top: 2rpx solid rgba(240, 240, 240, 0.8);
  z-index: 1000;
  box-sizing: border-box;
}

/* 快捷按钮区域 */
.quick-actions {
  margin-bottom: 20rpx;
  overflow-x: auto;
  overflow-y: hidden;
  width: 100%;
  -webkit-overflow-scrolling: touch;
}

.quick-buttons-container {
  display: flex;
  flex-direction: row;
  gap: 16rpx;
  padding: 0 20rpx;
  height: 80rpx;
  align-items: center;
  width: max-content;
  min-width: 100%;
}

.quick-btn {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 32rpx;
  padding: 16rpx 24rpx;
  border: 2rpx solid rgba(255, 255, 255, 1);
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 12rpx;
  font-size: 24rpx;
  color: rgba(23, 20, 51, 1);
  font-family: Microsoft YaHei UI-Bold, Microsoft YaHei, sans-serif;
  font-weight: 700;
  white-space: nowrap;
  flex-shrink: 0;
  min-width: 140rpx;
  height: 60rpx;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.quick-btn:hover {
  background-color: rgba(255, 255, 255, 1);
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.quick-btn:active {
  transform: scale(0.95);
  transition: transform 0.1s ease;
}

.quick-icon {
  width: 28rpx;
  height: 28rpx;
  flex-shrink: 0;
}

/* 输入容器 */
.input-container {
  width: 100%;
}

/* 问答按钮 */
.qa-btn {
  background: linear-gradient(180deg, rgba(228, 233, 253, 0.48) 0%, rgba(255, 255, 255, 0.8) 40.784153%);
  border-radius: 50rpx;
  width: 100%;
  height: 100rpx;
  border: 2rpx solid rgba(255, 255, 255, 1);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.qa-btn:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
}

.qa-icon-wrapper {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 50rpx;
  height: 80rpx;
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.qa-icon {
  width: 40rpx;
  height: 40rpx;
}

.qa-text {
  color: rgba(184, 191, 208, 1);
  font-size: 28rpx;
  letter-spacing: 2rpx;
  font-family: Microsoft YaHei UI-Bold, Microsoft YaHei, sans-serif;
  font-weight: 700;
  line-height: 28rpx;
  margin-left: 30rpx;
}

/* 输入框包装器 */
.input-wrapper {
  position: relative;
  width: 100%;
}

/* 输入框样式 */
.chat-input-box {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50rpx;
  width: 100%;
  height: 100rpx;
  border: 2rpx solid rgba(95, 88, 255, 1);
  padding: 0 100rpx 0 30rpx;
  font-size: 28rpx;
  outline: none;
  box-shadow: 0 4rpx 20rpx rgba(95, 88, 255, 0.2);
  box-sizing: border-box;
}

/* 发送按钮 */
.send-btn {
  position: absolute;
  right: 10rpx;
  top: 10rpx;
  width: 80rpx;
  height: 80rpx;
  background: rgba(95, 88, 255, 1);
  border: none;
  border-radius: 40rpx;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 24rpx;
  font-weight: 600;
}

.send-btn:hover {
  background: rgba(75, 68, 235, 1);
  transform: scale(1.05);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .chat-input {
    padding: 16rpx 24rpx 24rpx;
  }
  
  .quick-buttons-container {
    gap: 12rpx;
    padding: 0 16rpx;
    height: 70rpx;
  }

  .quick-btn {
    padding: 12rpx 20rpx;
    font-size: 22rpx;
    border-radius: 28rpx;
    min-width: 120rpx;
    height: 50rpx;
  }
  
  .quick-icon {
    width: 24rpx;
    height: 24rpx;
  }
  
  .qa-btn {
    height: 88rpx;
    border-radius: 44rpx;
  }
  
  .qa-icon-wrapper {
    width: 68rpx;
    height: 68rpx;
  }
  
  .qa-icon {
    width: 32rpx;
    height: 32rpx;
  }
  
  .qa-text {
    font-size: 24rpx;
    margin-left: 24rpx;
  }
  
  .chat-input-box {
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 26rpx;
    padding: 0 88rpx 0 24rpx;
  }
  
  .send-btn {
    width: 68rpx;
    height: 68rpx;
    border-radius: 34rpx;
    font-size: 22rpx;
  }
}

@media (max-width: 480px) {
  .chat-input {
    padding: 12rpx 20rpx 20rpx;
  }
  
  .quick-actions {
    margin-bottom: 16rpx;
  }
  
  .quick-buttons-container {
    gap: 10rpx;
    padding: 0 12rpx;
    height: 60rpx;
  }

  .quick-btn {
    padding: 10rpx 16rpx;
    font-size: 20rpx;
    border-radius: 24rpx;
    min-width: 100rpx;
    height: 40rpx;
  }
  
  .quick-icon {
    width: 20rpx;
    height: 20rpx;
  }
  
  .qa-btn {
    height: 80rpx;
    border-radius: 40rpx;
    padding: 0 24rpx;
  }
  
  .qa-icon-wrapper {
    width: 60rpx;
    height: 60rpx;
  }
  
  .qa-icon {
    width: 28rpx;
    height: 28rpx;
  }
  
  .qa-text {
    font-size: 22rpx;
    margin-left: 20rpx;
  }
  
  .chat-input-box {
    height: 80rpx;
    border-radius: 40rpx;
    font-size: 24rpx;
    padding: 0 80rpx 0 20rpx;
  }
  
  .send-btn {
    width: 60rpx;
    height: 60rpx;
    border-radius: 30rpx;
    font-size: 20rpx;
    right: 8rpx;
    top: 8rpx;
  }
}
</style>
